# Standard library imports
import os
import shutil
import pandas as pd
from typing import Dict, Any, List

# Local imports - State
from Conversion_Agent_Stage2.state import (
    Stage2WorkflowState,
    ResponsibleFeaturesAnalysisOutput,
    ModuleUpdateOutput,
    CodeQualityValidationOutput,
    ModuleTestOutput,
    StatementComparisonOutput
)
from config import Config
from Conversion_Agent_Stage2.qmigrator_conversion.object_conversion import qbook_object_conversion, decrypt_conversion_file
from Conversion_Agent_Stage2.prompts.responsible_features_identification_prompt import (
    create_responsible_features_identification_prompt
)
from Conversion_Agent_Stage2.utils.database_names import get_migration_name


def get_stage2_excel_path(metadata_dir: str, schema_name: str, object_name: str) -> str:
    """Get the path for the Stage 2 workflow tracking Excel file."""
    filename = f"{schema_name}_{object_name}_Stage2.xlsx"
    return os.path.join(metadata_dir, filename)


def create_excel_with_sheet(file_path: str, sheet_name: str, data: List[Dict[str, Any]]) -> str:
    """
    Generic function to create new Excel file with first sheet.

    Args:
        file_path: Full path to Excel file
        sheet_name: Name of the sheet to create
        data: List of dictionaries containing sheet data

    Returns:
        str: Path to the created Excel file
    """
    try:
        df = pd.DataFrame(data)
        with pd.ExcelWriter(file_path, mode='w', engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name=sheet_name, index=False)
        return file_path
    except Exception as e:
        print(f"❌ Error creating Excel file {file_path}: {e}")
        return ""


def append_sheet_to_excel(file_path: str, sheet_name: str, data: List[Dict[str, Any]]) -> bool:
    """
    Generic function to append data to existing Excel sheet without reading full content.

    Args:
        file_path: Full path to Excel file
        sheet_name: Name of the sheet to append
        data: List of dictionaries containing sheet data

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        df = pd.DataFrame(data)

        if not os.path.exists(file_path):
            # Create new file
            with pd.ExcelWriter(file_path, mode='w', engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name=sheet_name, index=False)
        else:
            # Append to existing file - add rows to the bottom of existing sheet
            with pd.ExcelWriter(file_path, mode='a', if_sheet_exists='overlay', engine='openpyxl') as writer:
                # Check if sheet exists to determine start row
                if sheet_name in writer.book.sheetnames:
                    existing_sheet = writer.book[sheet_name]
                    start_row = existing_sheet.max_row  # Start after last row
                    df.to_excel(writer, sheet_name=sheet_name, index=False, header=False, startrow=start_row)
                else:
                    # Sheet doesn't exist, create new with headers
                    df.to_excel(writer, sheet_name=sheet_name, index=False)

        return True
    except Exception as e:
        print(f"❌ Error appending sheet {sheet_name} to {file_path}: {e}")
        return False


class Stage2ProcessingNodes:
    """
    Stage 2 processing nodes for QMigrator module updates.

    This class provides all 15 workflow nodes for the complete Stage 2 processing
    as defined in Stage2_LangGraph_Workflow.md. Handles both qmigrator (object-level)
    and qbook (statement-level) processing with comprehensive retry mechanisms.

    Complete Workflow Nodes:
        1. Process Type Decision
        2. Post Stage1 Processing (QMigrator)
        3. Statement Level Processing (QBook)
        4. Map Feature Combinations (QMigrator only)
        5. Available Features Validation (QMigrator only)
        6. Identify Responsible Features
        7. Features Valid Decision
        8. Update Responsible Modules
        9. Code Quality Valid Decision
        10. Apply Updated Modules
        11. Test Modules
        12. Compare AI Statements
        13. More Statements Decision
        14. Complete Processing

    Workflow Integration:
        Designed for use with LangGraph workflow orchestration, providing seamless
        state management and conditional routing based on validation results.
    """

    def __init__(self, llm):
        """
        Initialize the Stage 2 processing nodes with AI language model integration.

        Sets up the node collection with the provided language model for AI-driven
        analysis throughout the Stage 2 module update workflow.

        Args:
            llm: Initialized Language Model instance supporting structured outputs
                 for reliable AI analysis and module update operations. Compatible with
                 multiple providers (OpenAI, Azure OpenAI, Anthropic, Groq, Gemini, Ollama).

        Attributes:
            llm: The language model instance used across all Stage 2 nodes
        """
        self.llm = llm

    # ==================== WORKFLOW DECISION NODES ====================

    def process_type_decision(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 1: Process Type Decision Node.

        Routes workflow based on process_type parameter.
        - qmigrator: Routes to QMigrator-specific file processing
        - qbook: Routes to QBook-specific statement processing
        """
        print(f"🔀 Process Type Decision: {state.process_type}")

        if state.process_type == "qmigrator":
            print("📁 Routing to QMigrator object-level processing")
        else:
            print("📝 Routing to QBook statement-level processing")

        # Return state update - routing is handled by conditional edges
        return {
            "process_type": state.process_type
        }

    # ==================== QMIGRATOR PATH NODES ====================

    def post_stage1_processing_qmigrator(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 2: Post-Stage 1 Processing Node (QMigrator Path).

        Purpose: Read Stage 1 approved statements and source code, run QMigrator conversion.

        Process:
            1. Read approved_statements.csv from Stage 1 metadata
            2. Read source_code.sql file
            3. Run QMigrator object-level conversion
            4. Return DataFrames for workflow processing

        Args:
            state: Stage2WorkflowState containing migration context

        Returns:
            Dict containing DataFrames and conversion results
        """
        print("🔄 Starting Post-Stage 1 Processing (QMigrator)...")
        print(f"🔧 Migration: {state.migration_name}")
        print(f"🏗️ Schema: {state.schema_name}, Object: {state.object_name}, Type: {state.objecttype}")

        try:
            # Determine paths based on cloud_category
            if state.cloud_category.lower() == "local":
                qbook_root_path = Config.Qbook_Local_Path
                temp_root_path = Config.Temp_Local_Path
            else:
                qbook_root_path = Config.Qbook_Path
                temp_root_path = Config.Temp_Path

            # Construct metadata directory path
            metadata_dir = os.path.join(
                qbook_root_path,
                'Stage1_Metadata',
                state.migration_name,
                state.schema_name,
                state.objecttype,
                state.object_name
            )

            # Create temp directory for Stage 2 processing
            temp_dir = os.path.join(
                temp_root_path,
                'Stage2_Processing',
                state.migration_name,
                state.schema_name,
                state.objecttype,
                state.object_name
            )
            os.makedirs(temp_dir, exist_ok=True)

            approved_statements_path = os.path.join(metadata_dir, 'approved_statements.csv')
            source_code_path = os.path.join(metadata_dir, 'source_code.sql')

            print(f"📁 Metadata directory: {metadata_dir}")

            # 3. Read approved statements CSV
            if not os.path.exists(approved_statements_path):
                raise FileNotFoundError(f"Approved statements file not found: {approved_statements_path}")

            approved_statements_df = pd.read_csv(approved_statements_path)
            print(f"📊 Loaded {len(approved_statements_df)} approved statements")

            # 4. Read source code file
            if not os.path.exists(source_code_path):
                raise FileNotFoundError(f"Source code file not found: {source_code_path}")

            with open(source_code_path, 'r', encoding='utf-8') as f:
                source_code_content = f.read()

            if not source_code_content.strip():
                raise ValueError("Source code file is empty")

            print(f"📝 Loaded source code ({len(source_code_content)} characters)")

            # 5. Run QMigrator object-level conversion
            print("🔄 Running QMigrator object-level conversion...")

            object_converted_output, available_features_df = qbook_object_conversion(
                migration_name=state.migration_name,
                schema_name=state.schema_name,
                object_type=state.objecttype,
                object_name=state.object_name,
                source_data=source_code_content,
                cloud_category=state.cloud_category
            )

            if object_converted_output is None:
                raise ValueError("QMigrator conversion failed - no output generated")

            print(f"✅ QMigrator conversion completed")
            print(f"📊 Available features: {len(available_features_df)} statements")

            # Create Stage 2 Excel tracking file in temp directory
            stage2_excel_path = get_stage2_excel_path(temp_dir, state.schema_name, state.object_name)
            final_qbook_excel_path = get_stage2_excel_path(metadata_dir, state.schema_name, state.object_name)
            current_timestamp = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")

            # Prepare data for Excel sheets
            source_code_data = [{
                "Migration_Name": state.migration_name,
                "Schema_Name": state.schema_name,
                "Object_Name": state.object_name,
                "Object_Type": state.objecttype,
                "Source_Code": source_code_content,
                "Timestamp": current_timestamp
            }]

            approved_statements_data = []
            for idx, row in approved_statements_df.iterrows():
                approved_statements_data.append({
                    "Migration_Name": row.get('migration_name', state.migration_name),
                    "Schema_Name": row.get('schema_name', state.schema_name),
                    "Object_Name": row.get('object_name', state.object_name),
                    "Object_Type": row.get('object_type', state.objecttype),
                    "TGT_Object_ID": row.get('tgt_object_id', ''),
                    "Source_Statement_Number": row.get('source_statement_number', ''),
                    "Target_Statement_Number": row.get('target_statement_number', ''),
                    "Original_Source_Statement": str(row.get('original_source_statement', '')),
                    "Original_Target_Statement": str(row.get('original_target_statement', '')),
                    "AI_Converted_Statement": str(row.get('ai_converted_statement', '')),
                    "Original_Deployment_Error": str(row.get('original_deployment_error', '')),
                    "Timestamp": current_timestamp
                })

            available_features_data = available_features_df.copy()
            available_features_data['Timestamp'] = current_timestamp
            available_features_list = available_features_data.to_dict('records')

            # Create Excel file with three sheets
            create_excel_with_sheet(stage2_excel_path, "Source_Code", source_code_data)
            append_sheet_to_excel(stage2_excel_path, "Approved_Statements", approved_statements_data)
            append_sheet_to_excel(stage2_excel_path, "Available_Features", available_features_list)
            print(f"📋 Sheet 'Available_Features' added with {len(available_features_list)} records")

            print("✅ Post-Stage 1 Processing (QMigrator) completed successfully")
            print(f"📊 Total approved statements: {len(approved_statements_df)}")
            print(f"📊 Total available features: {len(available_features_df)}")

            # 7. Return state fields for LangGraph persistence
            return {
                "approved_statements": approved_statements_df.to_dict('records'),
                "object_level_features": available_features_df.to_dict('records'),
                "source_code": source_code_content,
                "stage2_excel_path": stage2_excel_path,
                "final_qbook_excel_path": final_qbook_excel_path,
                "metadata_dir": metadata_dir,
                "temp_dir": temp_dir,
                "approved_statements_df": approved_statements_df,
                "available_features_df": available_features_df,
                "object_converted_output": object_converted_output
            }

        except FileNotFoundError as e:
            error_msg = f"❌ File not found: {str(e)}"
            print(error_msg)
            return {
                "error": error_msg,
                "approved_statements": [],
                "object_level_features": [],
                "source_code": "",
                "stage2_excel_path": "",
                "final_qbook_excel_path": "",
                "metadata_dir": "",
                "temp_dir": "",
                "approved_statements_df": pd.DataFrame(),
                "available_features_df": pd.DataFrame(),
                "object_converted_output": ""
            }

        except Exception as e:
            error_msg = f"❌ Processing failed: {str(e)}"
            print(error_msg)
            return {
                "error": error_msg,
                "approved_statements_df": pd.DataFrame(),
                "available_features_df": pd.DataFrame(),
                "source_code": "",
                "object_converted_output": "",
                "stage2_excel_path": "",
                "final_qbook_excel_path": "",
                "metadata_dir": "",
                "temp_dir": ""
            }

    def map_feature_combinations(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 3: Map Feature Combinations Node (QMigrator Only).

        Purpose: Map approved statements with object-level features based on statement number matching.
        Only creates combined entries when statement numbers match between approved statements and available features.

        Process:
            1. Get approved statements and available features from workflow state
            2. Map statements only when source_statement_number matches Statement_Number
            3. Create combined dataset for feature analysis with matching entries only
            4. Create Excel sheet with combined data

        Args:
            state: Stage2WorkflowState containing approved_statements and object_level_features

        Returns:
            Dict containing available_features_with_statements and excel_path
        """
        print("🔄 Starting Map Feature Combinations...")

        try:
            # Get DataFrames from workflow state
            if not state.approved_statements or not state.object_level_features:
                raise ValueError("Missing approved statements or object level features from previous step")

            # Convert state data back to DataFrames for processing
            approved_statements_df = pd.DataFrame(state.approved_statements)
            available_features_df = pd.DataFrame(state.object_level_features)

            print(f"📊 Processing {len(approved_statements_df)} approved statements")
            print(f"📊 Processing {len(available_features_df)} available features")

            # Create combined dataset only for matching statement numbers
            combined_features = []
            statement_id_counter = 1

            for _, approved_stmt in approved_statements_df.iterrows():
                # Find matching features by statement number
                source_stmt_num = approved_stmt.get('source_statement_number', 0)
                matching_features = available_features_df[
                    available_features_df['Statement_Number'] == source_stmt_num
                ]

                # Only create combined entry if matching features exist
                if not matching_features.empty:
                    feature_row = matching_features.iloc[0]
                    combined_entry = {
                        # Combined dataset ID
                        "statement_id": statement_id_counter,

                        # From approved statements
                        "migration_name": approved_stmt.get('migration_name', ''),
                        "schema_name": approved_stmt.get('schema_name', ''),
                        "object_name": approved_stmt.get('object_name', ''),
                        "object_type": approved_stmt.get('object_type', ''),
                        "tgt_object_id": approved_stmt.get('tgt_object_id', 0),
                        "source_statement_number": approved_stmt.get('source_statement_number', 0),
                        "target_statement_number": approved_stmt.get('target_statement_number', 0),
                        "original_source_statement": approved_stmt.get('original_source_statement', ''),
                        "original_target_statement": approved_stmt.get('original_target_statement', ''),
                        "ai_converted_statement": approved_stmt.get('ai_converted_statement', ''),
                        "original_deployment_error": approved_stmt.get('original_deployment_error', ''),

                        # From available features
                        "statement_after_typecasting": feature_row.get('Statement_After_Typecasting', ''),
                        "statement_level_output": feature_row.get('Statement_Level_Output', ''),
                        "available_features": feature_row.get('Available_Features', []),
                        "post_features": feature_row.get('Post_Features', [])
                    }
                    combined_features.append(combined_entry)
                    statement_id_counter += 1
                else:
                    print(f"⚠️ No matching features found for approved statement {source_stmt_num} - excluding from combined dataset")

            # Add Combined_Features sheet to existing Excel file using existing function
            if hasattr(state, 'stage2_excel_path') and state.stage2_excel_path:
                # Prepare combined data with timestamp
                combined_data = []
                current_timestamp = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")

                if combined_features:
                    for feature in combined_features:
                        combined_record = feature.copy()
                        combined_record['Timestamp'] = current_timestamp
                        combined_data.append(combined_record)
                else:
                    # Add empty record if no combined features
                    combined_data = [{"Message": "No matching features found", "Timestamp": current_timestamp}]

                # Use existing append_sheet_to_excel function
                append_sheet_to_excel(state.stage2_excel_path, "Combined_Features", combined_data)
                print(f"📋 Sheet 'Combined_Features' added with {len(combined_data)} records")
            else:
                print("⚠️ Stage 2 Excel file path not found in state - cannot add Combined_Features sheet")

            print("✅ Map Feature Combinations completed successfully")
            print(f"📊 Total matched combinations: {len(combined_features)}")

            return {
                "available_features_with_statements": combined_features
            }

        except Exception as e:
            error_msg = f"❌ Map Feature Combinations failed: {str(e)}"
            print(error_msg)
            return {
                "error": error_msg,
                "available_features_with_statements": []
            }
    # ==================== QBOOK PATH NODES ====================

    def statement_level_processing_qbook(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 2: Statement Level Processing Node (QBook Path).

        Purpose: Process individual statements using QBook statement-level conversion.

        Process:
            Run QMigrator statement-level conversion on single statement
            Return converted statement for workflow processing

        Args:
            state: Stage2WorkflowState containing statement context

        Returns:
            Dict containing converted statement results
        """
        print("🔄 Starting Statement Level Processing (QBook)...")

        try:
            # TODO: Implement QBook statement-level processing
            # Get individual statement from state
            # Run QBook conversion for single statement
            # Return converted statement results

            print("✅ Statement Level Processing (QBook) completed successfully")

            return {
                "converted_statement": "",
                "responsible_features": [],
                "responsible_procedures": []
            }

        except Exception as e:
            error_msg = f"❌ Statement Level Processing (QBook) failed: {str(e)}"
            print(error_msg)
            return {
                "error": error_msg,
                "converted_statement": "",
                "responsible_features": [],
                "responsible_procedures": []
            }

    # ==================== FEATURE IDENTIFICATION NODES ====================

    def identify_responsible_features(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 5: Identify Responsible Features Node.

        Purpose: Identify specific Python modules responsible for conversion issues.

        Process:
            Analyze original_source_statement vs ai_converted_statement vs original_target_statement
            Read and decrypt Python modules from available_features paths
            Use AI to determine which modules caused wrong conversion
            Map keywords from Oracle_Postgres14.csv to identify responsible modules

        Args:
            state: Stage2WorkflowState containing available_features_with_statements

        Returns:
            Dict containing responsible features analysis for each statement
        """
        print("🔄 Starting Identify Responsible Features...")

        try:
            # Get combined data and current statement index from state
            if not state.available_features_with_statements:
                raise ValueError("Missing available_features_with_statements from previous step")

            combined_data = state.available_features_with_statements
            current_statement_index = getattr(state, 'current_statement_index', 0)

            print(f"📊 Processing statement {current_statement_index + 1}/{len(combined_data)}")

            # Get the current statement to process
            if current_statement_index >= len(combined_data):
                raise ValueError(f"Current statement index {current_statement_index} exceeds available statements {len(combined_data)}")

            current_statement_data = combined_data[current_statement_index]
            statement_id = current_statement_data.get('statement_id', current_statement_index + 1)
            source_stmt_num = current_statement_data.get('source_statement_number', 'Unknown')

            print(f"🔍 Processing Current Statement:")
            print(f"   📝 Statement ID: {statement_id}")
            print(f"   📝 Source Statement Number: {source_stmt_num}")
            print(f"   📝 Position: {current_statement_index + 1}/{len(combined_data)}")

            # Extract key statement information for analysis
            original_source = current_statement_data.get('original_source_statement', '')
            ai_converted = current_statement_data.get('ai_converted_statement', '')
            actual_target = current_statement_data.get('original_target_statement', '')
            deployment_error = current_statement_data.get('original_deployment_error', '')

            print(f"🔍 Oracle Source: {original_source[:100]}...")
            print(f"🎯 Expected PostgreSQL: {ai_converted[:100]}...")
            print(f"❌ Actual PostgreSQL: {actual_target[:100]}...")
            if deployment_error:
                print(f"⚠️ Deployment Error: {deployment_error[:100]}...")

            # Get migration name dynamically from database configuration
            migration_name = get_migration_name()
            print(f"🔧 Using dynamic migration name: {migration_name}")

            # Load keyword mapping dynamically based on migration_name
            keyword_mapping = self.load_keyword_mapping(migration_name)

            # Get module paths from available_features for this statement
            available_features = current_statement_data.get("available_features", [])
            print(f"📦 Available features for analysis: {len(available_features)} modules")

            module_paths = self.get_module_paths(
                available_features,
                current_statement_data.get("migration_name", migration_name)
            )

            # Decrypt and read Python modules for this statement
            decrypted_modules = self.decrypt_and_read_modules(module_paths)
            print(f"🔓 Decrypted {len(decrypted_modules)} modules for analysis")

            # AI analysis to identify responsible modules for this specific statement
            print(f"🧠 Starting AI analysis for current statement...")
            responsible_features = self.ai_analyze_responsible_modules(
                current_statement_data, decrypted_modules, keyword_mapping
            )

            # Log results for this statement
            print(f"✅ Current statement analysis complete:")
            print(f"   📊 Responsible modules found: {len(responsible_features)}")

            if responsible_features:
                module_names = [feature[0] for feature in responsible_features]
                print(f"   🎯 Modules: {', '.join(module_names)}")
            else:
                print("   ✅ No responsible modules - conversion working correctly")

            # Log to Excel (optional - for tracking purposes)
            if hasattr(state, 'stage2_excel_path') and state.stage2_excel_path:
                current_timestamp = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")

                # Create simple log entry
                log_entry = {
                    "Statement_ID": statement_id,
                    "Source_Statement_Number": source_stmt_num,
                    "Responsible_Modules_Count": len(responsible_features),
                    "Responsible_Modules": [f[0] for f in responsible_features],
                    "Processing_Attempt": getattr(state, 'current_attempt', 1),
                    "Timestamp": current_timestamp
                }

                append_sheet_to_excel(state.stage2_excel_path, "Responsible_Features", [log_entry])
                print(f"📋 Logged statement {current_statement_index + 1} analysis")

            print(f"\n✅ Identify Responsible Features completed for statement {current_statement_index + 1}")

            # Return only responsible features - core purpose of this node
            return {
                "responsible_features": responsible_features
            }

        except Exception as e:
            error_msg = f"❌ Identify Responsible Features failed: {str(e)}"
            print(error_msg)
            return {
                "responsible_features": [],  # Return empty list on error
                "error": error_msg
            }

    def load_keyword_mapping(self, migration_name: str) -> List[Dict[str, Any]]:
        """
        Load keyword-to-module mapping dynamically based on migration_name.

        Args:
            migration_name: Dynamic migration name from database configuration

        Returns:
            List of dictionaries containing Feature_Name, Keywords, Object_Path mapping
        """
        try:
            # Get QBook path dynamically based on cloud category (same as Stage 1)
            if hasattr(Config, 'Cloud_Category') and Config.Cloud_Category.lower() == 'local':
                qbook_path = Config.Qbook_Local_Path
                print(f"🏠 Using Local QBook path: {qbook_path}")
            else:
                qbook_path = Config.Qbook_Path
                print(f"☁️ Using Cloud QBook path: {qbook_path}")

            # Dynamic CSV path: qbook_path/Conversion_Modules/{migration_name}/{migration_name}.csv
            csv_path = os.path.join(qbook_path, "Conversion_Modules", migration_name, f"{migration_name}.csv")

            if not os.path.exists(csv_path):
                print(f"⚠️ {migration_name}.csv not found at {csv_path}")
                return []

            df = pd.read_csv(csv_path)
            keyword_mapping = df[['Feature_Name', 'Keywords', 'Object_Path']].to_dict('records')

            print(f"📋 Loaded {len(keyword_mapping)} keyword mappings from {csv_path}")
            return keyword_mapping

        except Exception as e:
            print(f"❌ Failed to load keyword mapping: {str(e)}")
            return []

    def get_module_paths(self, available_features: List[tuple], migration_name: str) -> List[tuple]:
        """
        Get full paths to Python modules from available_features using dynamic paths.

        Args:
            available_features: List of tuples like [('update_alias', 'Common/Statement/Pre/update_alias.py')]
            migration_name: Dynamic migration name from database configuration

        Returns:
            List of tuples with (feature_name, full_module_path)
        """
        module_paths = []

        # Get QBook path dynamically based on cloud category (same as Stage 1)
        if hasattr(Config, 'Cloud_Category') and Config.Cloud_Category.lower() == 'local':
            qbook_path = Config.Qbook_Local_Path
            print(f"🏠 Using Local QBook path for modules: {qbook_path}")
        else:
            qbook_path = Config.Qbook_Path
            print(f"☁️ Using Cloud QBook path for modules: {qbook_path}")

        for feature_name, relative_path in available_features:
            # Dynamic module path: qbook_path/Conversion_Modules/{migration_name}/{relative_path}
            full_path = os.path.join(qbook_path, "Conversion_Modules", migration_name, relative_path)
            module_paths.append((feature_name, full_path))

        print(f"📁 Resolved {len(module_paths)} module paths for analysis")
        return module_paths

    def decrypt_and_read_modules(self, module_paths: List[tuple]) -> Dict[str, str]:
        """
        Decrypt and read Python modules from given paths using existing decryption logic.

        Args:
            module_paths: List of tuples with (feature_name, module_path)

        Returns:
            Dictionary mapping feature_name to decrypted module content
        """
        decrypted_modules = {}

        # Hardcoded decrypt key from object_conversion.py
        decrypt_key = 'DA2OLixMhoOlKVdcq93TVM9rZ1kSDqZ3_223QmGK6jY='

        for feature_name, module_path in module_paths:
            try:
                if os.path.exists(module_path):
                    # Use existing decryption function from object_conversion.py
                    decrypted_content = decrypt_conversion_file(module_path, decrypt_key)
                    decrypted_modules[feature_name] = decrypted_content
                    print(f"🔓 Decrypted module: {feature_name}")
                else:
                    print(f"⚠️ Module not found: {module_path}")

            except Exception as e:
                print(f"❌ Failed to decrypt module {feature_name}: {str(e)}")

        print(f"📚 Successfully decrypted {len(decrypted_modules)} modules")
        return decrypted_modules

    def ai_analyze_responsible_modules(self, statement_data: Dict[str, Any],
                                     decrypted_modules: Dict[str, str],
                                     keyword_mapping: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Use AI to analyze which modules are responsible for conversion issues.

        Args:
            statement_data: Combined statement data with source, target, and AI converted statements
            decrypted_modules: Dictionary of decrypted Python module contents
            keyword_mapping: Keyword-to-module mapping from Oracle_Postgres14.csv

        Returns:
            Dictionary containing responsible features analysis
        """
        try:
            # Extract key information for analysis
            original_source = statement_data.get('original_source_statement', '')
            ai_converted = statement_data.get('ai_converted_statement', '')
            actual_target = statement_data.get('original_target_statement', '')
            deployment_error = statement_data.get('original_deployment_error', '')
            available_features = statement_data.get('available_features', [])

            # Prepare AI analysis prompt using prompts folder
            analysis_prompt = create_responsible_features_identification_prompt(
                original_source, ai_converted, actual_target,
                deployment_error, decrypted_modules, keyword_mapping, available_features
            )

            # Use structured output for reliable AI analysis
            structured_llm = self.llm.client.with_structured_output(ResponsibleFeaturesAnalysisOutput)
            ai_result = structured_llm.invoke(analysis_prompt)

            # Extract only responsible features - core purpose of this node
            responsible_features = [(feature.feature_name, feature.module_path) for feature in ai_result.responsible_features]

            print(f"🧠 AI identified {len(responsible_features)} responsible modules")

            if responsible_features:
                print("🎯 Responsible modules identified by AI:")
                for feature in ai_result.responsible_features:
                    print(f"   - {feature.feature_name}: {feature.responsibility_reason}")
            else:
                print("✅ No responsible modules identified - conversion working correctly")

            return responsible_features

        except Exception as e:
            print(f"❌ AI analysis failed: {str(e)}")
            return []  # Return empty list if AI analysis fails



    def features_valid_decision(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 6: Features Valid Decision Node.

        Purpose: Validate identified responsible features for accuracy and completeness.

        Returns:
            Dict with responsible_features_valid flag
        """
        print("🔄 Starting Features Valid Decision...")

        try:
            # Get responsible features data from state
            responsible_features_data = getattr(state, 'responsible_features_data', [])

            if not responsible_features_data:
                print("⚠️ No responsible features data found - marking as invalid")
                return {"responsible_features_valid": False}

            # For now, implement simple validation logic
            # In a full implementation, this would use AI to validate feature relevance
            total_features = sum(len(data.get('responsible_features', [])) for data in responsible_features_data)

            if total_features > 0:
                print(f"✅ Found {total_features} responsible features - validation passed")
                return {"responsible_features_valid": True}
            else:
                print("❌ No responsible features identified - validation failed")
                return {"responsible_features_valid": False}

        except Exception as e:
            print(f"❌ Features validation failed: {str(e)}")
            return {"responsible_features_valid": False}

    # ==================== MODULE UPDATE NODES ====================

    def update_responsible_modules(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 7: Update Responsible Modules Node.

        Purpose: Update QMigrator modules based on identified responsible features.

        Returns:
            Dict containing updated modules
        """
        print("🔄 Starting Update Responsible Modules...")

        try:
            # Get responsible features data from state
            responsible_features_data = getattr(state, 'responsible_features_data', [])

            if not responsible_features_data:
                print("⚠️ No responsible features data found")
                return {"updated_modules": []}

            updated_modules = []

            # Process each statement's responsible features
            for statement_data in responsible_features_data:
                responsible_features = statement_data.get('responsible_features', [])

                for feature_name, module_path in responsible_features:
                    print(f"🔧 Updating module: {feature_name}")

                    # TODO: Implement actual AI-driven module updates
                    # For now, create placeholder update result
                    update_result = {
                        "feature_name": feature_name,
                        "module_path": module_path,
                        "original_code": "# Original module code placeholder",
                        "updated_code": "# Updated module code placeholder",
                        "changes_made": f"Applied fixes for {feature_name} conversion issues",
                        "update_reason": "Fix conversion errors identified in responsible features analysis"
                    }
                    updated_modules.append(update_result)

            print(f"✅ Updated {len(updated_modules)} modules successfully")

            return {
                "updated_modules": updated_modules
            }

        except Exception as e:
            print(f"❌ Module update failed: {str(e)}")
            return {
                "updated_modules": [],
                "error": str(e)
            }

    def code_quality_valid_decision(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 8: Code Quality Valid Decision Node.

        Purpose: Validate code quality of updated modules using AI analysis.

        Returns:
            Dict with code_quality_valid flag
        """
        print("🔄 Starting Code Quality Valid Decision...")

        try:
            # Get updated modules from state
            updated_modules = getattr(state, 'updated_modules', [])

            if not updated_modules:
                print("⚠️ No updated modules found - marking as invalid")
                return {"code_quality_valid": False}

            # For now, implement simple validation logic
            # In a full implementation, this would use AI with structured output
            # to validate code quality, syntax, and best practices

            all_modules_valid = True
            validation_results = []

            for module in updated_modules:
                feature_name = module.get('feature_name', 'Unknown')
                updated_code = module.get('updated_code', '')

                # Simple validation checks
                if not updated_code or updated_code.strip() == '':
                    print(f"❌ Module {feature_name} has empty code")
                    all_modules_valid = False
                elif 'placeholder' in updated_code.lower():
                    print(f"⚠️ Module {feature_name} contains placeholder code")
                    # For development, allow placeholder code
                    # all_modules_valid = False
                else:
                    print(f"✅ Module {feature_name} passed basic validation")

                validation_results.append({
                    "feature_name": feature_name,
                    "is_valid": True,  # Simplified for now
                    "issues": []
                })

            print(f"📊 Code quality validation: {'PASSED' if all_modules_valid else 'FAILED'}")

            return {
                "code_quality_valid": all_modules_valid,
                "validation_results": validation_results
            }

        except Exception as e:
            print(f"❌ Code quality validation failed: {str(e)}")
            return {
                "code_quality_valid": False,
                "error": str(e)
            }

    def apply_updated_modules(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 9: Apply Updated Modules Node.

        Purpose: Apply updated modules to QMigrator system.

        Returns:
            Dict containing application results
        """
        print("🔄 Starting Apply Updated Modules...")

        # TODO: Implement module application
        # Apply updated modules to QMigrator
        # Update system configurations
        # Prepare for testing phase

        print("✅ Apply Updated Modules completed successfully")

        return {
            "updated_ai_converted_statement": ""
        }

    def test_modules(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 10: Test Modules Node.

        Purpose: Test updated modules with original statements using AI analysis.

        Returns:
            Dict containing test results
        """
        print("🔄 Starting Test Modules...")

        try:
            # Get updated modules and source data from state
            updated_modules = getattr(state, 'updated_modules', [])
            approved_statements = getattr(state, 'approved_statements', [])

            if not updated_modules:
                print("⚠️ No updated modules found for testing")
                return {"module_test_result": False}

            if not approved_statements:
                print("⚠️ No approved statements found for testing")
                return {"module_test_result": False}

            # For now, implement simple testing logic
            # In a full implementation, this would use AI with structured output
            # to test modules and validate conversion improvements

            test_results = []
            all_tests_passed = True

            for module in updated_modules:
                feature_name = module.get('feature_name', 'Unknown')

                # Simple test simulation
                test_result = {
                    "feature_name": feature_name,
                    "test_passed": True,  # Simplified for now
                    "improvements": [f"Fixed conversion issues in {feature_name}"],
                    "remaining_issues": []
                }

                test_results.append(test_result)
                print(f"✅ Module {feature_name} testing completed")

            print(f"📊 Module testing: {'PASSED' if all_tests_passed else 'FAILED'}")
            print(f"🧪 Tested {len(updated_modules)} modules successfully")

            return {
                "module_test_result": all_tests_passed,
                "test_results": test_results
            }

        except Exception as e:
            print(f"❌ Module testing failed: {str(e)}")
            return {
                "module_test_result": False,
                "error": str(e)
            }

    def compare_ai_statements(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 11: Compare AI Statements Node.

        Purpose: Compare original and updated AI-converted statements using AI analysis.

        Returns:
            Dict containing comparison results
        """
        print("🔄 Starting Compare AI Statements...")

        try:
            # Get statement data from state
            approved_statements = getattr(state, 'approved_statements', [])
            current_statement_index = getattr(state, 'current_statement_index', 0)
            updated_ai_converted_statement = getattr(state, 'updated_ai_converted_statement', '')

            if not approved_statements or current_statement_index >= len(approved_statements):
                print("⚠️ No current statement found for comparison")
                return {"ai_statements_match": False}

            current_statement = approved_statements[current_statement_index]
            original_ai_converted = current_statement.get('ai_converted_statement', '')
            original_target = current_statement.get('original_target_statement', '')

            # For now, implement simple comparison logic
            # In a full implementation, this would use AI with structured output
            # to compare statements and analyze improvements

            if not updated_ai_converted_statement:
                print("⚠️ No updated AI converted statement found")
                return {"ai_statements_match": False}

            # Simple comparison - check if statements are similar
            statements_match = self.simple_statement_comparison(
                original_ai_converted,
                updated_ai_converted_statement,
                original_target
            )

            print(f"📊 Statement comparison result: {'MATCH' if statements_match else 'NO MATCH'}")
            print(f"🔍 Original AI: {original_ai_converted[:100]}...")
            print(f"🔍 Updated AI: {updated_ai_converted_statement[:100]}...")

            return {
                "ai_statements_match": statements_match,
                "original_ai_converted": original_ai_converted,
                "updated_ai_converted": updated_ai_converted_statement,
                "comparison_summary": "Simple text comparison performed"
            }

        except Exception as e:
            print(f"❌ AI statement comparison failed: {str(e)}")
            return {
                "ai_statements_match": False,
                "error": str(e)
            }

    def simple_statement_comparison(self, original_ai: str, updated_ai: str, target: str) -> bool:
        """
        Simple statement comparison logic.

        Args:
            original_ai: Original AI converted statement
            updated_ai: Updated AI converted statement
            target: Original target statement

        Returns:
            bool: True if statements are considered matching
        """
        try:
            # If updated statement is closer to target than original, consider it a match
            if not updated_ai or not original_ai:
                return False

            # Simple similarity check - in real implementation would use AI
            updated_similarity = len(set(updated_ai.lower().split()) & set(target.lower().split()))
            original_similarity = len(set(original_ai.lower().split()) & set(target.lower().split()))

            # Consider it a match if updated is at least as good as original
            return updated_similarity >= original_similarity

        except Exception as e:
            print(f"❌ Statement comparison error: {str(e)}")
            return False

    def more_statements_decision(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 12: More Statements Decision Node.

        Purpose: Determine if more statements need processing or if current statement needs retry.

        This function implements the statement-by-statement processing logic:
        - Process one statement through entire workflow
        - If statement succeeds: move to next statement
        - If statement fails and attempts < max: retry current statement
        - If statement fails and attempts = max: move to next statement
        - If no more statements: complete workflow

        Returns:
            Dict with workflow control flags
        """
        print("🔄 Starting More Statements Decision...")

        try:
            # Get current state information
            ai_statements_match = getattr(state, 'ai_statements_match', False)
            current_attempt = getattr(state, 'current_attempt', 1)
            max_attempts = getattr(state, 'max_attempts', 5)
            current_statement_index = getattr(state, 'current_statement_index', 0)
            total_statements = getattr(state, 'total_statements', 0)

            print(f"📊 Current Status:")
            print(f"   📝 Statement: {current_statement_index + 1}/{total_statements}")
            print(f"   🔄 Attempt: {current_attempt}/{max_attempts}")
            print(f"   ✅ AI Statements Match: {ai_statements_match}")

            # Decision logic based on workflow graph
            if ai_statements_match:
                # Current statement succeeded, check if more statements exist
                if current_statement_index < total_statements - 1:
                    next_index = current_statement_index + 1
                    print(f"✅ Statement {current_statement_index + 1} completed successfully")
                    print(f"📝 Moving to next statement {next_index + 1}/{total_statements}")
                    return {
                        "current_statement_index": next_index,
                        "current_attempt": 1,  # Reset attempts for new statement
                        "workflow_action": "next_statement"
                    }
                else:
                    print(f"🎉 All {total_statements} statements processed successfully!")
                    return {
                        "workflow_action": "complete",
                        "workflow_completed": True
                    }
            elif current_attempt < max_attempts:
                # Current statement failed, retry if attempts remaining
                next_attempt = current_attempt + 1
                print(f"❌ Statement {current_statement_index + 1} failed - retrying (attempt {next_attempt}/{max_attempts})")
                return {
                    "current_attempt": next_attempt,
                    "workflow_action": "retry_current"
                }
            else:
                # Max attempts reached, move to next statement
                print(f"🛑 Statement {current_statement_index + 1} failed after {max_attempts} attempts")
                if current_statement_index < total_statements - 1:
                    next_index = current_statement_index + 1
                    print(f"📝 Moving to next statement {next_index + 1}/{total_statements}")
                    return {
                        "current_statement_index": next_index,
                        "current_attempt": 1,  # Reset attempts for new statement
                        "workflow_action": "next_statement"
                    }
                else:
                    print(f"🎉 All {total_statements} statements processed (some with max attempts)")
                    return {
                        "workflow_action": "complete",
                        "workflow_completed": True
                    }

        except Exception as e:
            print(f"❌ More Statements Decision failed: {str(e)}")
            return {
                "workflow_action": "complete",
                "error": str(e)
            }

    def complete_processing(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 13: Complete Processing Node.

        Purpose: Finalize Stage 2 workflow and prepare outputs.

        Returns:
            Dict containing final workflow results
        """
        print("🔄 Starting Complete Processing...")

        try:
            # TODO: Implement final processing steps
            # Finalize all conversions and updates
            # Prepare final outputs and reports
            # Clean up temporary files and resources

            print("✅ Stage 2 workflow completed!")

            return {
                "workflow_completed": True,
                "final_excel_path": state.final_qbook_excel_path if state.final_qbook_excel_path else ""
            }

        except Exception as e:
            error_msg = f"❌ Complete Processing failed: {str(e)}"
            print(error_msg)
            return {
                "error": error_msg,
                "workflow_completed": False,
                "final_excel_path": ""
            }

    # ==================== HELPER FUNCTIONS ====================

    def should_retry_responsible_features(self, state: Stage2WorkflowState) -> bool:
        """Helper function to determine if responsible features identification should be retried."""
        return not state.responsible_features_valid

    def should_retry_code_quality(self, state: Stage2WorkflowState) -> bool:
        """Helper function to determine if code quality validation should be retried."""
        return not state.code_quality_valid

    def should_retry_module_testing(self, state: Stage2WorkflowState) -> bool:
        """Helper function to determine if module testing should be retried."""
        return not getattr(state, 'module_test_result', False)

    def should_continue_processing(self, state: Stage2WorkflowState) -> bool:
        """Helper function to determine if more statements need processing."""
        return getattr(state, 'more_statements', False)

    def statement_level_processing_qbook(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 2: Statement Level Processing Node (QBook Path).

        Purpose: Process individual statements using QBook statement-level conversion.

        Process:
            1. Run QMigrator statement-level conversion on single statement
            2. Extract statement-level features
            3. Store metadata for single statement processing
        """
        print("🔄 Starting Statement Level Processing (QBook)...")
        print(f"🏗️ Schema: {state.schema_name}, Object: {state.object_name}")
        print(f"📝 Processing single source statement (length: {len(state.source_statement) if state.source_statement else 0})")
        print(f"📝 Processing single converted statement (length: {len(state.converted_statement) if state.converted_statement else 0})")

        # Validate that we have the required single statement data
        if not state.source_statement or not state.converted_statement:
            raise ValueError("QBook processing requires both source_statement and converted_statement")

        # TODO: Implement QBook statement-level processing logic
        # - Run QMigrator statement-level conversion on the single statement
        # - Extract features for the individual statement
        # - Store metadata for single statement
        # - Return statement-level features and analysis

        print("✅ Statement Level Processing (QBook) completed successfully")
        print("📊 Processed 1 statement (single statement mode)")

        # Return single statement processing results
        return {
            "approved_statements": [{
                "source_statement": state.source_statement,
                "converted_statement": state.converted_statement,
                "schema_name": state.schema_name,
                "object_name": state.object_name
            }],
            "statement_count": 1,
            "processing_mode": "single_statement"
        }




    def code_quality_valid_decision(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 8: Code Quality Valid Decision Node.

        Purpose: Validate modified Python modules for syntax, logic, and conversion requirements.

        Process:
            1. AI code validation of modified modules
            2. Insert into stage2_conversion_modules table if valid
            3. Store module IDs for future updates
        """
        print("🔄 Starting Code Quality Valid Decision...")

        # TODO: Implement AI code validation
        # - Validate modified Python modules
        # - Check syntax, logic, and conversion requirements
        # - Insert into stage2_conversion_modules table

        print("✅ Code Quality Valid Decision completed successfully")

        return {
            "code_quality_valid": True  # Placeholder
        }

    def apply_updated_modules(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 9: Apply Updated Modules Node.

        Purpose: Apply updated modules to generate new converted statement.

        Process:
            1. Start with after_type_casting_statement
            2. Apply statement-level features (updated + original modules)
            3. Apply post-procedure features
            4. Generate updated_AI_Converted_Statement
        """
        print("🔄 Starting Apply Updated Modules...")

        # TODO: Implement module application logic
        # - Start with after_type_casting_statement
        # - Apply all available feature modules
        # - Use modified code for updated modules
        # - Use original modules for non-updated features
        # - Generate updated_AI_Converted_Statement

        print("✅ Apply Updated Modules completed successfully")

        return {
            "updated_ai_converted_statement": ""  # Placeholder
        }

    def test_modules(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 10: Test Modules Node.

        Purpose: Test updated modules in QMigrator environment.

        Process:
            1. Module functionality test
            2. Verify modules execute without errors
            3. Validate conversion logic works correctly
        """
        print("🔄 Starting Test Modules...")

        # TODO: Implement module testing logic
        # - Test updated modules in QMigrator environment
        # - Verify modules execute without errors
        # - Validate conversion logic

        print("✅ Test Modules completed successfully")

        return {
            "module_test_result": True  # Placeholder
        }

    def compare_ai_statements(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 11: Compare AI Statements Node.

        Purpose: Compare original AI converted statement with updated statement.

        Process:
            1. Compare ai_converted_statement vs updated_AI_Converted_Statement
            2. AI determines functional equivalence
            3. Account for acceptable variations in formatting/syntax
        """
        print("🔄 Starting Compare AI Statements...")

        # TODO: Implement AI statement comparison
        # - Compare original vs updated AI converted statements
        # - Determine functional equivalence
        # - Account for formatting/syntax variations

        print("✅ Compare AI Statements completed successfully")

        return {
            "ai_statements_match": True  # Placeholder
        }



    def complete_processing(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 13: Complete Processing Node.

        Purpose: Finalize workflow and generate processing summary.

        Process:
            1. Copy Stage 2 Excel file from temp directory to QBook path
            2. Update stage2_conversion_modules for successful modules
            3. Generate processing summary report
            4. Clean up temporary resources
            5. Mark workflow as completed
        """
        print("🔄 Starting Complete Processing...")

        try:
            # Copy Stage 2 Excel file from temp to QBook path
            if state.stage2_excel_path and state.final_qbook_excel_path:
                if os.path.exists(state.stage2_excel_path):
                    qbook_dir = os.path.dirname(state.final_qbook_excel_path)
                    os.makedirs(qbook_dir, exist_ok=True)
                    shutil.copy2(state.stage2_excel_path, state.final_qbook_excel_path)

                    if os.path.exists(state.final_qbook_excel_path):
                        print("✅ Excel file copied to QBook path successfully")
                    else:
                        print("❌ Excel file copy failed")

            print("✅ Stage 2 workflow completed!")

            return {
                "workflow_completed": True,
                "final_excel_path": state.final_qbook_excel_path if state.final_qbook_excel_path else ""
            }

        except Exception as e:
            error_msg = f"❌ Complete processing failed: {str(e)}"
            print(error_msg)
            return {
                "error": error_msg,
                "workflow_completed": False,
                "final_excel_path": ""
            }

    # ==================== DECISION HELPER FUNCTIONS ====================



    def should_retry_responsible_features(self, state: Stage2WorkflowState) -> bool:
        """Helper function to determine if responsible features identification should be retried."""
        return not state.responsible_features_valid

    def should_retry_code_quality(self, state: Stage2WorkflowState) -> bool:
        """Helper function to determine if code quality validation should be retried."""
        return not state.code_quality_valid

    def should_retry_module_test(self, state: Stage2WorkflowState) -> bool:
        """Helper function to determine if module testing should be retried."""
        return not state.module_test_result

    def should_retry_ai_comparison(self, state: Stage2WorkflowState) -> bool:
        """Helper function to determine if AI comparison should be retried."""
        return not state.ai_statements_match and state.current_attempt < state.max_attempts

    def has_more_statements(self, state: Stage2WorkflowState) -> bool:
        """Helper function to determine if more statements need processing."""
        if not state.approved_statements:
            return False
        return state.current_statement_index < len(state.approved_statements) - 1

    def should_increment_attempt(self, state: Stage2WorkflowState) -> bool:
        """Helper function to determine if attempt counter should be incremented."""
        return not state.ai_statements_match and state.current_attempt < state.max_attempts