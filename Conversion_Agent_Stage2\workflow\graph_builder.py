import uuid, os
from typing import Dict, Any
from langgraph.graph import <PERSON><PERSON>raph, END, START
from langgraph.checkpoint.memory import MemorySaver
from langchain_core.runnables.graph import MermaidD<PERSON>Method
from Conversion_Agent_Stage2.state import Stage2WorkflowState
from Conversion_Agent_Stage2.nodes import Stage2ProcessingNodes


class Stage2GraphBuilder:
    """
    Advanced Stage 2 workflow graph builder for QMigrator module updates.

    This class constructs and manages a sophisticated AI-driven workflow graph that handles
    the complete Stage 2 QMigrator module update process. It orchestrates multiple nodes
    including process type routing, feature identification, module updates, validation loops,
    and statement-by-statement processing using LangGraph for state management.

    Key Features:
        - Dual-path processing (QMigrator object-level vs QBook statement-level)
        - AI-driven feature identification and module analysis
        - Iterative module update loops with validation
        - Statement-by-statement processing with retry mechanisms
        - Comprehensive state management and audit trails
        - Support for complex module update scenarios with 5-attempt limits

    Workflow Architecture:
        The graph implements a sophisticated flow with validation loops at each critical step,
        ensuring high-quality module updates through iterative AI improvement and comprehensive testing.
        Supports both qmigrator (object-level) and qbook (statement-level) processing paths.
    """

    def __init__(self, llm):
        """
        Initialize the Stage 2 workflow graph builder with AI language model integration.

        Sets up the core components for the QMigrator module update workflow including
        the LangGraph state management system, memory checkpointing for workflow persistence,
        and integration with the provided language model for AI-driven analysis.

        Args:
            llm: Initialized Language Model instance for AI-driven module analysis and updates.
                 Supports multiple providers (OpenAI, Azure OpenAI, Anthropic, Groq, Gemini, Ollama)
                 configured for optimal QMigrator module update performance.

        Attributes:
            llm: The language model instance for AI operations
            builder: LangGraph StateGraph builder for workflow construction
            memory: MemorySaver for workflow state persistence and checkpointing
            nodes: Stage2ProcessingNodes instance for workflow operations
        """
        self.llm = llm
        self.builder = StateGraph(Stage2WorkflowState)
        self.memory = MemorySaver()
        self.nodes = Stage2ProcessingNodes(llm)

    def build_graph(self):
        """
        Configure the complete Stage 2 QMigrator module update workflow graph.

        Constructs a sophisticated AI-driven workflow with multiple validation loops and
        conditional routing for optimal QMigrator module updates. The graph includes
        14 specialized nodes that handle process type routing, feature identification,
        module updates, validation, and statement processing with iterative improvement.

        Workflow Architecture:
            1. Process Type Decision: Route between qmigrator/qbook processing paths
            2. QMigrator Path: Post-Stage1 → Map Features → Validate Features → Common Path
            3. QBook Path: Statement Processing → Common Path
            4. Common Path: Identify Features → Validate → Update → Test → Compare
            5. Statement Loop: Process all statements with 5-attempt retry mechanism
            6. Complete Processing: Finalize workflow and generate summary

        Conditional Routing:
            - Process type routing directs to appropriate processing path
            - Validation failures trigger feedback loops for AI improvement
            - Module update failures restart with updated code (max 5 attempts per statement)
            - Statement processing continues until all statements are processed
            - Successful completion or max attempts reached terminates the workflow

        Returns:
            StateGraph: Configured workflow builder ready for compilation and execution
        """
        # Create fresh builder instance to avoid cached nodes and ensure clean state
        self.builder = StateGraph(Stage2WorkflowState)
        self.conversion_nodes = Stage2ProcessingNodes(llm=self.llm)

        # Add all workflow nodes for comprehensive QMigrator module updates
        self.builder.add_node("process_type_decision", self.conversion_nodes.process_type_decision)
        self.builder.add_node("post_stage1_processing_qmigrator", self.conversion_nodes.post_stage1_processing_qmigrator)
        self.builder.add_node("map_feature_combinations", self.conversion_nodes.map_feature_combinations)
        self.builder.add_node("statement_level_processing_qbook", self.conversion_nodes.statement_level_processing_qbook)
        self.builder.add_node("identify_responsible_features", self.conversion_nodes.identify_responsible_features)
        self.builder.add_node("features_valid_decision", self.conversion_nodes.features_valid_decision)
        self.builder.add_node("update_responsible_modules", self.conversion_nodes.update_responsible_modules)
        self.builder.add_node("code_quality_valid_decision", self.conversion_nodes.code_quality_valid_decision)
        self.builder.add_node("apply_updated_modules", self.conversion_nodes.apply_updated_modules)
        self.builder.add_node("test_modules", self.conversion_nodes.test_modules)
        self.builder.add_node("compare_ai_statements", self.conversion_nodes.compare_ai_statements)
        self.builder.add_node("more_statements_decision", self.conversion_nodes.more_statements_decision)
        self.builder.add_node("complete_processing", self.conversion_nodes.complete_processing)

        # Define linear workflow progression with validation checkpoints
        self.builder.add_edge(START, "process_type_decision")
        # self.builder.add_edge("process_type_decision", "post_stage1_processing_qmigrator")
        # self.builder.add_edge("post_stage1_processing_qmigrator", 'map_feature_combinations')
        # self.builder.add_edge("map_feature_combinations", "complete_processing")
        # self.builder.add_edge("complete_processing", END)
        
        # Process type decision routing
        self.builder.add_conditional_edges(
            "process_type_decision",
            self.should_route_to_qmigrator_or_qbook,
            {
                "qmigrator": "post_stage1_processing_qmigrator",  # Object-level processing
                "qbook": "statement_level_processing_qbook"  # Statement-level processing
            }
        )

        # QMigrator path progression - direct flow without validation
        self.builder.add_edge("post_stage1_processing_qmigrator", "map_feature_combinations")
        self.builder.add_edge("map_feature_combinations", "identify_responsible_features")

        # QBook path progression
        self.builder.add_edge("statement_level_processing_qbook", "identify_responsible_features")

        # Common path progression with validation loops
        self.builder.add_edge("identify_responsible_features", "features_valid_decision")

        # Features validation with feedback loop for responsible feature improvement
        self.builder.add_conditional_edges(
            "features_valid_decision",
            self.should_continue_responsible_features,
            {
                "continue": "identify_responsible_features",  # Retry with feedback
                "proceed": "update_responsible_modules"  # Continue to module updates
            }
        )

        self.builder.add_edge("update_responsible_modules", "code_quality_valid_decision")

        # Code quality validation with feedback loop for module improvement
        self.builder.add_conditional_edges(
            "code_quality_valid_decision",
            self.should_continue_code_quality,
            {
                "continue": "update_responsible_modules",  # Retry with feedback
                "proceed": "apply_updated_modules"  # Continue to module application
            }
        )

        # Linear progression through module application and testing
        self.builder.add_edge("apply_updated_modules", "test_modules")

        # Module testing with feedback loop for module correction
        self.builder.add_conditional_edges(
            "test_modules",
            self.should_continue_module_testing,
            {
                "continue": "update_responsible_modules",  # Retry module updates
                "proceed": "compare_ai_statements"  # Continue to AI comparison
            }
        )

        self.builder.add_edge("compare_ai_statements", "more_statements_decision")

        # Statement processing loop with attempt management
        self.builder.add_conditional_edges(
            "more_statements_decision",
            self.should_continue_or_complete,
            {
                "next_statement": "identify_responsible_features",  # Process next statement
                "retry_current": "update_responsible_modules",  # Retry current statement
                "complete": "complete_processing"  # All statements processed
            }
        )

        self.builder.add_edge("complete_processing", END)

        return self.builder

    def setup_graph(self):
        """Setup and compile the Stage 2 workflow graph."""
        builder = self.build_graph()
        self.graph = builder.compile(
            interrupt_before=[], checkpointer=self.memory
        )
        return self.graph

    def invoke_graph(self, data: Dict[str, Any], thread_id: str = None) -> Dict[str, Any]:
        """
        Invoke the graph with the given input data.

        Args:
            data: Dictionary containing the input data for the workflow
            thread_id: Optional thread ID for the workflow execution

        Returns:
            Dictionary containing the workflow result
        """
        thread_id = thread_id or f"thread_{uuid.uuid4()}"
        thread = {"configurable": {"thread_id": thread_id}, "recursion_limit": 200}
        return self.graph.invoke(data, config=thread)

    def should_route_to_qmigrator_or_qbook(self, state: Stage2WorkflowState) -> str:
        """
        Determine routing based on process type.

        Args:
            state: Current workflow state containing process type

        Returns:
            "qmigrator" for object-level processing, "qbook" for statement-level processing
        """
        process_type = getattr(state, 'process_type', 'qmigrator')

        if process_type == "qmigrator":
            print("🔀 Routing to QMigrator object-level processing path")
            return "qmigrator"
        else:
            print("🔀 Routing to QBook statement-level processing path")
            return "qbook"



    def should_continue_responsible_features(self, state: Stage2WorkflowState) -> str:
        """
        Determine if responsible features validation should continue or proceed to next step.

        Args:
            state: Current workflow state containing responsible features validation results

        Returns:
            "continue" to retry responsible features, "proceed" to move to module updates
        """
        responsible_features_valid = getattr(state, 'responsible_features_valid', False)

        if responsible_features_valid:
            print("✅ Responsible features validation successful - proceeding to module updates")
            return "proceed"
        else:
            print("❌ Responsible features validation failed - retrying feature identification")
            return "continue"

    def should_continue_code_quality(self, state: Stage2WorkflowState) -> str:
        """
        Determine if code quality validation should continue or proceed to next step.

        Args:
            state: Current workflow state containing code quality validation results

        Returns:
            "continue" to retry module updates, "proceed" to move to module application
        """
        code_quality_valid = getattr(state, 'code_quality_valid', False)

        if code_quality_valid:
            print("✅ Code quality validation successful - proceeding to module application")
            return "proceed"
        else:
            print("❌ Code quality validation failed - retrying module updates")
            return "continue"

    def should_continue_module_testing(self, state: Stage2WorkflowState) -> str:
        """
        Determine if module testing should continue or proceed to next step.

        Args:
            state: Current workflow state containing module test results

        Returns:
            "continue" to retry module updates, "proceed" to move to AI comparison
        """
        module_test_result = getattr(state, 'module_test_result', False)

        if module_test_result:
            print("✅ Module testing successful - proceeding to AI statement comparison")
            return "proceed"
        else:
            print("❌ Module testing failed - retrying module updates")
            return "continue"

    def should_continue_or_complete(self, state: Stage2WorkflowState) -> str:
        """
        Determine if we should continue processing statements, retry current statement, or complete workflow.

        Stage 2 Statement-Based Decision Logic:
            - If AI statements don't match and attempts < max: Retry current statement
            - If AI statements don't match and attempts = max: Move to next statement (use latest attempt)
            - If AI statements match: Move to next statement
            - If no more statements: Complete workflow

        Args:
            state: Current workflow state containing statement processing status

        Returns:
            "retry_current", "next_statement", or "complete"
        """
        ai_statements_match = getattr(state, 'ai_statements_match', False)
        current_attempt = getattr(state, 'current_attempt', 1)
        max_attempts = getattr(state, 'max_attempts', 5)
        approved_statements = getattr(state, 'approved_statements', [])
        current_statement_index = getattr(state, 'current_statement_index', 0)

        if ai_statements_match:
            print("✅ AI statements match - moving to next statement")
            if current_statement_index < len(approved_statements) - 1:
                print(f"📝 Processing next statement ({current_statement_index + 2}/{len(approved_statements)})")
                return "next_statement"
            else:
                print("🎉 All statements processed successfully - completing workflow")
                return "complete"
        elif current_attempt < max_attempts:
            print(f"❌ AI statements don't match - retrying current statement (attempt {current_attempt + 1}/{max_attempts})")
            return "retry_current"
        else:
            print(f"🛑 Maximum attempts reached for current statement - using latest attempt and moving to next")
            if current_statement_index < len(approved_statements) - 1:
                print(f"📝 Processing next statement ({current_statement_index + 2}/{len(approved_statements)})")
                return "next_statement"
            else:
                print("🎉 All statements processed (some with max attempts) - completing workflow")
                return "complete"

    def save_graph_image(self, graph):
        """
        Save the workflow graph as a PNG image using Mermaid.

        Args:
            graph: Compiled LangGraph workflow
        """
        try:
            # Generate the PNG image
            img_data = graph.get_graph().draw_mermaid_png(
                draw_method=MermaidDrawMethod.API
            )

            # Save the image to a file
            graph_path = os.path.join("Conversion_Agent_Stage2", "stage2_workflow_graph.png")
            with open(graph_path, "wb") as f:
                f.write(img_data)

            print(f"Stage 2 graph image saved to {graph_path}")
        except Exception as e:
            print(f"Warning: Could not generate Stage 2 graph image: {str(e)}")
            print("Continuing execution without graph visualization...")