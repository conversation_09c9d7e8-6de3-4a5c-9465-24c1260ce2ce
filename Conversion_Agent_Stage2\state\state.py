"""
Stage 2 Workflow State Management for QMigrator Module Updates.

This module defines the state management for Stage 2 processing, which handles
AI corrections from Stage 1 to update QMigrator Python modules through a
comprehensive 15-step workflow with retry mechanism.
"""

from typing import List, Optional, Dict, Any, Literal
from pydantic import BaseModel, Field


# ==================== STRUCTURED OUTPUT MODELS FOR AI RESPONSES ====================

class ResponsibleFeature(BaseModel):
    """
    Pydantic model for a single responsible feature identified by AI.

    Represents a Python module that is responsible for conversion issues,
    including detailed analysis of why it's responsible and its impact.
    """
    feature_name: str = Field(description="Name of the responsible feature/module")
    module_path: str = Field(description="Relative path to the Python module")
    responsibility_reason: str = Field(description="Detailed explanation of why this module is responsible")
    error_impact: str = Field(description="Impact level: High, Medium, Low")
    keywords_matched: List[str] = Field(description="Keywords that triggered this module identification")
    confidence_score: float = Field(description="AI confidence score between 0.0 and 1.0")


class ResponsibleFeaturesAnalysisOutput(BaseModel):
    """
    Simplified Pydantic model for AI-generated responsible features analysis.

    Focuses only on identifying which Python modules are responsible for conversion issues.
    """
    responsible_features: List[ResponsibleFeature] = Field(description="List of identified responsible features with detailed analysis")


class ModuleUpdateResult(BaseModel):
    """
    Pydantic model for a single module update result.

    Represents the result of updating a Python module to fix conversion issues.
    """
    feature_name: str = Field(description="Name of the updated feature/module")
    module_path: str = Field(description="Path to the updated module")
    original_code: str = Field(description="Original module code before updates")
    updated_code: str = Field(description="Updated module code with fixes")
    changes_made: str = Field(description="Detailed description of changes made")
    update_reason: str = Field(description="Reason for the specific updates")


class ModuleUpdateOutput(BaseModel):
    """
    Pydantic model for AI-generated module update results.

    Captures the complete output from AI module update operations.
    """
    updated_modules: List[ModuleUpdateResult] = Field(description="List of updated modules with detailed changes")
    update_summary: str = Field(description="Summary of all module updates performed")
    expected_improvements: str = Field(description="Expected improvements from the updates")
    potential_risks: List[str] = Field(description="Potential risks or side effects of the updates")


class CodeQualityValidationOutput(BaseModel):
    """
    Pydantic model for AI code quality validation results.

    Validates the quality of updated Python modules.
    """
    is_valid: bool = Field(description="Whether the code quality validation passed")
    quality_score: float = Field(description="Code quality score between 0.0 and 1.0")
    syntax_errors: List[str] = Field(description="List of syntax errors found")
    logic_issues: List[str] = Field(description="List of logic issues identified")
    best_practice_violations: List[str] = Field(description="List of best practice violations")
    recommendations: List[str] = Field(description="Recommendations for improvement")
    explanation: str = Field(description="Detailed explanation of the validation results")


class ModuleTestOutput(BaseModel):
    """
    Pydantic model for AI module testing results.

    Captures the results of testing updated modules in QMigrator environment.
    """
    test_passed: bool = Field(description="Whether the module testing passed")
    test_results: List[str] = Field(description="Detailed test results for each module")
    conversion_improvements: List[str] = Field(description="Improvements observed in conversion")
    remaining_issues: List[str] = Field(description="Issues that still remain after updates")
    performance_impact: str = Field(description="Impact on conversion performance")
    explanation: str = Field(description="Detailed explanation of test results")


class StatementComparisonOutput(BaseModel):
    """
    Pydantic model for AI statement comparison results.

    Compares original and updated AI-converted statements.
    """
    statements_match: bool = Field(description="Whether the statements are functionally equivalent")
    similarity_score: float = Field(description="Similarity score between 0.0 and 1.0")
    improvements_made: List[str] = Field(description="List of improvements made in the updated statement")
    remaining_differences: List[str] = Field(description="Differences that still remain")
    functional_equivalence: bool = Field(description="Whether statements are functionally equivalent")
    explanation: str = Field(description="Detailed explanation of the comparison results")


class Stage2WorkflowState(BaseModel):
    """
    Comprehensive workflow state for Stage 2 QMigrator module update processing.

    Implements the complete 15-step workflow as per Stage2_LangGraph_Workflow.md
    """

    # Core input parameters
    migration_name: str = Field(description="Migration name (e.g., Oracle_Postgres14)")
    process_type: Literal["qmigrator", "qbook"] = Field(description="Process type: qmigrator (object-level) or qbook (statement-level)")
    schema_name: str = Field(description="Schema name for the object")
    object_name: str = Field(description="Name of the object being processed")
    cloud_category: str = Field(description="Cloud category: 'local' or 'cloud'")

    # Process type specific fields
    objecttype: Optional[str] = Field(default=None, description="Type of the object (required for qmigrator)")
    source_statement: Optional[str] = Field(default=None, description="Individual source statement (required for qbook)")
    converted_statement: Optional[str] = Field(default=None, description="Individual converted statement (required for qbook)")

    # Optional fields
    target_object_id: Optional[int] = Field(default=None, description="Target object ID from Stage 1")

    # Source and target code
    source_code: Optional[str] = Field(default=None, description="Complete source code")
    target_code: Optional[str] = Field(default=None, description="Complete target code")

    # Processing results
    approved_statements: Optional[List[Dict[str, Any]]] = Field(default=None, description="List of approved statements from Stage 1")
    object_level_features: Optional[List[Dict[str, Any]]] = Field(default=None, description="Object-level feature analysis results")
    available_features_with_statements: Optional[List[Dict[str, Any]]] = Field(default=None, description="Combined approved statements with features")

    # Current processing state
    current_statement_index: int = Field(default=0, description="Index of current statement being processed")
    current_statement: Optional[Dict[str, Any]] = Field(default=None, description="Current statement being processed")

    # Feature identification
    responsible_features: Optional[List[tuple]] = Field(default=None, description="Responsible statement-level features as (name, path) tuples")
    responsible_procedures: Optional[List[str]] = Field(default=None, description="Responsible program-level features")
    responsible_features_data: Optional[List[Dict[str, Any]]] = Field(default=None, description="Detailed responsible features analysis with modules")

    # Module updates
    updated_modules: Optional[List[Dict[str, Any]]] = Field(default=None, description="Updated Python modules")
    module_test_result: Optional[bool] = Field(default=None, description="Module functionality test result")
    updated_ai_converted_statement: Optional[str] = Field(default=None, description="Statement after applying updated modules")

    # Attempt tracking
    current_attempt: int = Field(default=1, description="Current attempt number (max 5)")
    max_attempts: int = Field(default=5, description="Maximum attempts per statement")

    # Database tracking
    current_statement_id: Optional[int] = Field(default=None, description="Database ID of current statement")

    # Workflow control flags
    responsible_features_valid: bool = Field(default=False, description="Responsible features validation passed")
    code_quality_valid: bool = Field(default=False, description="Code quality validation passed")
    ai_statements_match: bool = Field(default=False, description="AI statements comparison passed")

    # Final results
    completed_statements: List[Dict[str, Any]] = Field(default_factory=list, description="Successfully processed statements")
    failed_statements: List[Dict[str, Any]] = Field(default_factory=list, description="Statements that failed after max attempts")
    workflow_completed: bool = Field(default=False, description="Entire workflow completed")

    # Excel tracking
    stage2_excel_path: Optional[str] = Field(default=None, description="Temp path to Stage 2 Excel file")
    final_qbook_excel_path: Optional[str] = Field(default=None, description="Final QBook path for Stage 2 Excel file")
    metadata_dir: Optional[str] = Field(default=None, description="Stage 1 metadata directory path")
    temp_dir: Optional[str] = Field(default=None, description="Temp directory for Stage 2 processing")