"""
Prompts for identifying responsible features in Stage 2 conversion analysis.
"""
from typing import Dict, List, Any
from Conversion_Agent_Stage2.utils.database_names import get_database_specific_terms


def create_responsible_features_identification_prompt(
    original_source: str,
    ai_converted: str,
    actual_target: str,
    deployment_error: str,
    decrypted_modules: Dict[str, str],
    keyword_mapping: List[Dict[str, Any]],
    available_features: List[tuple]
) -> str:
    """
    Create AI analysis prompt for identifying responsible modules (Stage 2 style with dynamic database names).

    Args:
        original_source: Original source database statement
        ai_converted: Expected target database statement from AI
        actual_target: Actual wrong target database statement
        deployment_error: Error message from deployment
        decrypted_modules: Dictionary of decrypted Python module contents
        keyword_mapping: Keyword-to-module mapping from CSV
        available_features: List of available feature tuples

    Returns:
        Formatted prompt string for AI analysis with structured output
    """

    # Get dynamic database names
    db_terms = get_database_specific_terms()
    source_db = db_terms['source_db']
    target_db = db_terms['target_db']
    migration_direction = db_terms['migration_direction']
    expert_title = db_terms['expert_title']

    # Format available features for prompt
    available_features_str = "\n".join([f"- {name}: {path}" for name, path in available_features])

    prompt = f"""
You are a {expert_title}. Your task is to identify which specific Python modules from the available features are responsible for {migration_direction} conversion failures.

CONVERSION ANALYSIS:
===================

Original {source_db} Statement:
{original_source}

Expected {target_db} (AI Generated):
{ai_converted}

Actual Result (Wrong):
{actual_target}

Deployment Error:
{deployment_error}

AVAILABLE FEATURES TO ANALYZE:
=============================
{available_features_str}

DECRYPTED MODULE CODE:
=====================
"""
    
    for module_name, module_code in decrypted_modules.items():
        prompt += f"\n--- {module_name.upper()} MODULE ---\n{module_code}\n"

    prompt += f"""

KEYWORD MAPPING REFERENCE:
=========================
{keyword_mapping}

ANALYSIS TASK:
=============
1. Understand that {source_db} and {target_db} statements will ALWAYS be different (different database languages)
2. Compare Expected {target_db} (what AI should have generated) with Actual Result (what was actually generated)
3. Identify conversion failures: where Expected ≠ Actual Result
4. Analyze the Deployment Error to understand specific failure points
5. Review each available feature's decrypted code to understand what each module should convert
6. **PRIMARY GOAL**: Identify which specific modules failed to perform their intended {source_db} to {target_db} conversion

IDENTIFICATION CRITERIA:
=======================
- {source_db} keywords/syntax present in source that should trigger specific conversion modules
- Modules that should have converted {source_db} syntax but failed (Expected ≠ Actual)
- Deployment errors that point to specific module conversion failures
- Missing {target_db} syntax that specific modules should have generated
- Incorrect {target_db} syntax that indicates module conversion logic errors



IMPORTANT: Do NOT expect {source_db} and {target_db} to match - they are different database languages!

OUTPUT FORMAT (JSON):
====================
{{
  "responsible_features": [
    {{
      "feature_name": "<feature_name>",
      "module_path": "<relative_path>",
      "responsibility_reason": "<why this module is responsible>",
      "error_impact": "High|Medium|Low",
      "keywords_matched": ["<keyword1>", "<keyword2>"],
      "confidence_score": <float between 0.0 and 1.0>
    }}
  ]
}}

EXAMPLES:
=========
Example 1 - Modules ARE responsible (conversion failed):
{{
  "responsible_features": [
    {{
      "feature_name": "sysdate",
      "module_path": "Common/Statement/Pre/sysdate.py",
      "responsibility_reason": "Module should convert SYSDATE to CURRENT_TIMESTAMP but failed",
      "error_impact": "High",
      "keywords_matched": ["sysdate"],
      "confidence_score": 0.9
    }}
  ]
}}

Example 2 - NO modules responsible (conversion working correctly):
{{
  "responsible_features": []
}}

IMPORTANT RULES:
===============
Return empty responsible_features array [] ONLY if:
- Expected {target_db} matches Actual {target_db} (conversion worked correctly)
- No deployment errors occurred
- All {source_db} syntax was properly converted to {target_db}

Return responsible modules if:
- Expected {target_db} ≠ Actual {target_db} (conversion failed)
- Deployment errors indicate conversion problems
- {source_db} syntax was not properly converted to {target_db}

Focus ONLY on identifying responsible modules. Do NOT suggest code changes.
"""
    
    return prompt