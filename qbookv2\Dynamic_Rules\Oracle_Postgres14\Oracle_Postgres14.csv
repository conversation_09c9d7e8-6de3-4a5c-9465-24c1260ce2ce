Migration_Name,Object_Identifiers,Comment_Identifiers,New_Line_Identifiers,Object_Path,Object_Id
Oracle_Postgres14,Start: Create or replace package End:Object-1&Start: Create or replace editionable package End:Object-1&Start: Create package End:Object-1,Start:/* End:*/&Start:-- End:\n,,Package,ObjectTypes object (7)
Oracle_Postgres14,Start:create table End:object-1&Start:create table End:;&Start:create global temporary table End:;,Start:/* End:*/&Start:-- End:\n,,Table,ObjectTypes object (13)
Oracle_Postgres14,Start: Create or replace function End:Object-1&Start: Create or replace editionable function End:Object-1&Start: Create function End:Object-1,Start:/* End:*/&Start:-- End:\n,,Function,ObjectTypes object (4)
Oracle_Postgres14,Start: Create or replace trigger End:Object-1&Start: Create or replace editionable trigger End:Object-1&Start: Create trigger End:Object-1,Start:/* End:*/&Start:-- End:\n,,Trigger,ObjectTypes object (112)
Oracle_Postgres14,Start: Create or replace procedure End:is|as&Start: Create procedure End:is|as&Start:as|is End:;&Start:;+1 End:;&Start:select End:;&Start: Create or replace editionable procedure End:is|as,Start: /* End: */&Start: -- End: \n,,Procedure/Statement,ObjectTypes object (16)
Oracle_Postgres14,Start: Create or replace procedure End:Object-1&Start: Create or replace  editionable procedure End:Object-1&Start: Create procedure End:Object-1,Start:/* End:*/&Start:-- End:\n,,Procedure,ObjectTypes object (1)
Oracle_Postgres14,Start: Create or replace function End:is|as&Start: Create function End:is|as&Start:as|is End:;&Start:;+1 End:;&Start: Create or replace editionable function End:is|as,Start: /* End: */&Start: -- End: \n,,Function/Statement,ObjectTypes object (25)
Oracle_Postgres14,Start: Create or replace force view End:Object-1&Start: Create or replace force view End:;,Start:/* End:*/&Start:-- End:\n,,View,ObjectTypes object (10)
Oracle_Postgres14,,,,Common,ObjectTypes object (103)
Oracle_Postgres14,Start:Select End:;&Start:insert End:;,Start:/* End:*/&Start:-- End:\n,,Query,ObjectTypes object (73)
Oracle_Postgres14,Start:Alter table End:object-1&Start:Alter table End:;,,,Foreign_Key,ObjectTypes object (58)
Oracle_Postgres14,Start: Create or replace type End:Object-1&Start: Create or replace type End:;,Start:/* End:*/&Start:-- End:\n,,Type,ObjectTypes object (169)
Oracle_Postgres14,Start: Create sequence End:Object-1&Start: Create sequence End:;,,,Sequence,ObjectTypes object (85)
Oracle_Postgres14,Start:Create materialized view End:object-1&Start:Create materialized view End:;,,,Materialized_View,ObjectTypes object (88)
Oracle_Postgres14,Start:Alter table End:object-1&Start:Alter table End:;,,,Primary_Key,ObjectTypes object (55)
Oracle_Postgres14,Start:Create or replace synonym End:object-1&Start:Create or replace synonym End:;&Start:Create synonym End:object-1&Start:Create synonym End:;,,,Synonym,ObjectTypes object (91)
Oracle_Postgres14,Start: Alter table End:;&Start:Alter table End:object-1,,,Check_Constraint,ObjectTypes object (52)
Oracle_Postgres14,Start:Alter table End:object-1&Start:Alter table End:;,,,Unique_Constraint,ObjectTypes object (175)
Oracle_Postgres14,Start:Alter table End:object-1&Start:Alter table End:;,,,Default_Constraint,ObjectTypes object (178)
Oracle_Postgres14,Start:Alter table End:object-1&Start:Alter table End:;,,,Not_Null_Constraint,ObjectTypes object (181)
Oracle_Postgres14,Start:create table End:object-1&Start:create global temporary table End:;&Start:create table End:;,Start:/* End:*/&Start:-- End:\n,,Temporary_Table,ObjectTypes object (184)
Oracle_Postgres14,Start:create table End:object-1&Start:create table End:;,,,Partition,ObjectTypes object (49)
Oracle_Postgres14,Start:Create unique index End:;&Start:Create unique index End:object-1&Start:Create index End:;&Start:Create index End:object-1,,,Index,ObjectTypes object (172)
